/* CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Light theme colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-accent: #e2e8f0;
    --text-primary: #1a202c;
    --text-secondary: #4a5568;
    --text-muted: #718096;
    --border-color: #e2e8f0;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --accent-color: #3182ce;
    --accent-hover: #2c5aa0;
    --success-color: #38a169;
    --danger-color: #e53e3e;
    --transition: all 0.3s ease;
}

[data-theme="dark"] {
    /* Dark theme colors */
    --bg-primary: #1a202c;
    --bg-secondary: #2d3748;
    --bg-accent: #4a5568;
    --text-primary: #f7fafc;
    --text-secondary: #e2e8f0;
    --text-muted: #a0aec0;
    --border-color: #4a5568;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3);
    --accent-color: #63b3ed;
    --accent-hover: #4299e1;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-primary);
    transition: var(--transition);
    min-height: 100vh;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 1.5rem 1rem;
    min-height: 100vh;
    max-height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Header Styles */
.header {
    text-align: center;
    margin-bottom: 1.5rem;
    flex-shrink: 0;
}

.title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    background: linear-gradient(135deg, var(--accent-color), #805ad5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.subtitle {
    font-size: 1.1rem;
    color: var(--text-secondary);
    font-weight: 400;
}

/* Main Content */
.main {
    flex: 1;
    position: relative;
    display: flex;
    flex-direction: column;
    min-height: 0;
}

/* Theme Toggle */
.theme-toggle {
    position: absolute;
    top: -1rem;
    right: 0;
    z-index: 10;
}

.theme-btn {
    background: var(--bg-secondary);
    border: 2px solid var(--border-color);
    border-radius: 50%;
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow);
}

.theme-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.theme-icon {
    font-size: 1.2rem;
}

/* Filter Section */
.filter-section {
    margin-bottom: 1rem;
    text-align: center;
    flex-shrink: 0;
}

.filter-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-secondary);
}

.category-filter {
    padding: 0.75rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: 0.5rem;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition);
    min-width: 200px;
}

.category-filter:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
}

/* Quote Display */
.quote-container {
    background: var(--bg-secondary);
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    transition: var(--transition);
    min-height: 180px;
    max-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    overflow: hidden;
}

.quote-display {
    text-align: center;
    width: 100%;
}

.quote-text {
    font-size: 1.25rem;
    font-weight: 400;
    line-height: 1.8;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    font-style: italic;
    position: relative;
}

.quote-text::before {
    content: '"';
    font-size: 3rem;
    color: var(--accent-color);
    position: absolute;
    top: -1rem;
    left: -1rem;
    font-family: Georgia, serif;
}

.quote-text::after {
    content: '"';
    font-size: 3rem;
    color: var(--accent-color);
    position: absolute;
    bottom: -2rem;
    right: -1rem;
    font-family: Georgia, serif;
}

.quote-author {
    font-size: 1rem;
    color: var(--text-secondary);
    font-weight: 500;
    font-style: normal;
}

.quote-author::before {
    content: '— ';
}

/* Buttons */
.button-container {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 1rem;
    flex-shrink: 0;
}

.quote-btn {
    padding: 0.875rem 2rem;
    border: none;
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 160px;
    justify-content: center;
}

.quote-btn.primary {
    background: var(--accent-color);
    color: white;
    box-shadow: var(--shadow);
}

.quote-btn.primary:hover {
    background: var(--accent-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.quote-btn.secondary {
    background: var(--bg-accent);
    color: var(--text-primary);
    border: 2px solid var(--border-color);
}

.quote-btn.secondary:hover {
    background: var(--border-color);
    transform: translateY(-1px);
}

.quote-btn.danger {
    background: var(--danger-color);
    color: white;
}

.quote-btn.danger:hover {
    background: #c53030;
    transform: translateY(-1px);
}

/* Favorites Section */
.favorites-section {
    margin-top: 1rem;
    padding: 1.5rem;
    background: var(--bg-secondary);
    border-radius: 1rem;
    border: 1px solid var(--border-color);
    max-height: 200px;
    overflow-y: auto;
    flex-shrink: 0;
}

.favorites-title {
    color: var(--text-primary);
    margin-bottom: 1rem;
    text-align: center;
}

.favorites-list {
    margin-bottom: 1.5rem;
}

.favorite-item {
    background: var(--bg-primary);
    padding: 1rem;
    margin-bottom: 0.5rem;
    border-radius: 0.5rem;
    border: 1px solid var(--border-color);
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* Footer */
.footer {
    text-align: center;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
    color: var(--text-muted);
    font-size: 0.85rem;
    flex-shrink: 0;
}

/* Desktop optimizations */
@media (min-width: 1024px) {
    .container {
        padding: 1rem 1rem;
        max-height: 100vh;
    }

    .header {
        margin-bottom: 1rem;
    }

    .title {
        font-size: 2.25rem;
        margin-bottom: 0.25rem;
    }

    .subtitle {
        font-size: 1.1rem;
    }

    .quote-container {
        padding: 2rem;
        min-height: 200px;
        max-height: 280px;
    }

    .quote-text {
        font-size: 1.3rem;
        line-height: 1.7;
    }

    .favorites-section {
        max-height: 180px;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 1rem 0.75rem;
    }
    
    .title {
        font-size: 2rem;
    }
    
    .quote-container {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    
    .quote-text {
        font-size: 1.1rem;
    }
    
    .quote-text::before,
    .quote-text::after {
        font-size: 2rem;
    }
    
    .quote-text::before {
        top: -0.5rem;
        left: -0.5rem;
    }
    
    .quote-text::after {
        bottom: -1.5rem;
        right: -0.5rem;
    }
    
    .button-container {
        flex-direction: column;
        align-items: center;
    }
    
    .quote-btn {
        width: 100%;
        max-width: 280px;
    }
    
    .theme-toggle {
        position: static;
        text-align: right;
        margin-bottom: 1rem;
    }
}

@media (max-width: 480px) {
    .quote-container {
        padding: 1rem;
    }

    .category-filter {
        width: 100%;
        max-width: 280px;
    }

    .title {
        font-size: 1.75rem;
    }

    .subtitle {
        font-size: 1rem;
    }
}

/* Additional mobile optimizations */
@media (max-width: 360px) {
    .container {
        padding: 0.75rem 0.5rem;
    }

    .quote-container {
        padding: 0.75rem;
        min-height: 150px;
    }

    .quote-text {
        font-size: 1rem;
        line-height: 1.6;
    }

    .title {
        font-size: 1.5rem;
    }
}

/* Notification styles */
.notification {
    font-family: inherit;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

@media (max-width: 768px) {
    .notification {
        right: 10px;
        left: 10px;
        transform: translateY(-100%);
    }

    .notification.show {
        transform: translateY(0);
    }
}

/* Favorite item styles */
.favorite-item {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 1rem;
}

.favorite-quote {
    flex: 1;
    line-height: 1.5;
}

.remove-favorite {
    background: var(--danger-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    transition: var(--transition);
    flex-shrink: 0;
}

.remove-favorite:hover {
    background: #c53030;
    transform: scale(1.1);
}

/* Loading and animation states */
.quote-container {
    transition: transform 0.2s ease, opacity 0.2s ease;
}

.quote-btn:active {
    transform: translateY(0) scale(0.98);
}

/* Focus styles for accessibility */
.quote-btn:focus,
.category-filter:focus,
.theme-btn:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --text-secondary: #000000;
    }

    [data-theme="dark"] {
        --border-color: #ffffff;
        --text-secondary: #ffffff;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .quote-container {
        transition: none;
    }
}
