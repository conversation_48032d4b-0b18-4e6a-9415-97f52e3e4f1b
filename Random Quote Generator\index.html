<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Random Quote Generator</title>
    <meta name="description" content="Get inspired with random quotes from various categories - motivational, funny, tech, and wisdom quotes.">
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1 class="title">Random Quote Generator</h1>
            <p class="subtitle">Get inspired, laugh, or reflect with a click</p>
        </header>

        <main class="main">
            <!-- Theme Toggle -->
            <div class="theme-toggle">
                <button id="themeToggle" class="theme-btn" aria-label="Toggle dark mode">
                    <span class="theme-icon">🌙</span>
                </button>
            </div>

            <!-- Category Filter -->
            <div class="filter-section">
                <label for="categoryFilter" class="filter-label">Choose a category:</label>
                <select id="categoryFilter" class="category-filter">
                    <option value="all">All Categories</option>
                    <option value="inspirational">Inspirational</option>
                    <option value="funny">Funny</option>
                    <option value="tech">Tech/Coding</option>
                    <option value="wisdom">Deep Thoughts</option>
                    <option value="sassy">Short & Sassy</option>
                </select>
            </div>

            <!-- Quote Display Area -->
            <div class="quote-container">
                <div id="quoteDisplay" class="quote-display">
                    <blockquote class="quote-text">
                        Click the button below to get your first inspiring quote!
                    </blockquote>
                    <cite class="quote-author"></cite>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="button-container">
                <button id="newQuoteBtn" class="quote-btn primary">
                    Show Me a Quote
                </button>
                <button id="favoriteBtn" class="quote-btn secondary" style="display: none;">
                    ❤️ Save to Favorites
                </button>
                <button id="shareBtn" class="quote-btn secondary" style="display: none;">
                    📤 Share Quote
                </button>
            </div>

            <!-- Favorites Section -->
            <div class="favorites-section" style="display: none;">
                <h3 class="favorites-title">Your Favorite Quotes</h3>
                <div id="favoritesList" class="favorites-list"></div>
                <button id="clearFavoritesBtn" class="quote-btn danger">Clear All Favorites</button>
            </div>
        </main>

        <footer class="footer">
            <p>&copy; 2025 Random Quote Generator. Made with ❤️ for inspiration.</p>
        </footer>
    </div>

    <script src="quotes.js"></script>
    <script src="script.js"></script>
</body>
</html>
